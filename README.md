# CoveredCalls-Agents Platform

An autonomous agent platform for options trading strategies using smolagents and E2B sandboxes. The system provides secure, monitored execution of trading strategies with comprehensive health monitoring, memory management, and observability features.

## 🚀 Core Capabilities

The platform is built on four foundational pillars that ensure robust, secure, and intelligent operation:

*   **🧠 Memory & Learning**: Agents maintain long-term memory using pgvector embeddings, allowing them to learn from past decisions and market patterns. Supports multiple memory backends including custom pgvector and mem0 integration.

*   **🔍 Real-time Observability**: Complete system visibility through Phoenix tracing with real-time monitoring of agent operations, performance metrics, and error tracking. All operations are traced and correlated with memory storage.

*   **🔒 Secure Execution**: Every agent and tool runs in isolated E2B sandboxes using smolagents' native E2B support. This prevents runaway code and secures the platform's infrastructure with automatic cleanup and audit logging.

*   **📊 Health Monitoring**: Continuous agent performance evaluation with automatic health gating. Underperforming agents are automatically sidelined to ensure only effective models remain active.

## 🛠️ Quick Start

### Prerequisites
- Python 3.9+
- Docker and Docker Compose
- Virtual environment (recommended)
- Required API keys (see configuration below)

### 1. Environment Setup

Clone the repository and set up your environment:
```bash
git clone <repository-url>
cd coveredcalls-agents
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 2. Configuration

Create a `.env` file in the project root:
```bash
# Required API Keys
SUPABASE_URL=your_supabase_project_url
SUPABASE_KEY=your_supabase_service_role_key
OPENAI_API_KEY=your_openai_api_key
E2B_API_KEY=your_e2b_api_key
POLYGON_API_KEY=your_polygon_api_key

# Phoenix Observability (optional but recommended)
PHOENIX_COLLECTOR_ENDPOINT=http://localhost:6006/v1/traces
PHOENIX_PROJECT_NAME=coveredcalls-agents
PHOENIX_ENABLE_TRACING=true
```

### 3. Database Setup

Set up your Supabase database with the required schemas:
```bash
# Run these SQL files in your Supabase SQL editor:
# sql/migrations/002_exec_audit_tables.sql
# sql/migrations/003_memory_system.sql
```

### 4. Start the Application

**Option A: Quick Start (Recommended)**
```bash
source venv/bin/activate
./start.sh
```

**Option B: Manual Start**
```bash
# Start Phoenix observability (optional)
docker-compose -f docker-compose.phoenix.yml up -d

# Start the application
source venv/bin/activate
python app.py
```

### 5. Access Points

Once running, you can access:
- **Application**: http://localhost:8000
- **Phoenix UI**: http://localhost:6006 (if enabled)
- **API Documentation**: Available at the root URL

## 🌐 API Endpoints

The platform provides a RESTful API for interacting with the agent system:

### Core Endpoints

**Execute Task**
```bash
curl -X POST http://localhost:8000/execute_task \
     -H "Content-Type: application/json" \
     -d '{"task": "Analyze AAPL for covered call opportunities"}'
```

**System Status**
```bash
curl http://localhost:8000/status
```

**Available Tools**
```bash
curl http://localhost:8000/tools
```

**Health Check**
```bash
curl http://localhost:8000/health
```

## 🏗️ System Architecture

### Memory System
- **PgVector Backend**: Custom implementation using Supabase + pgvector for fast financial operations
- **Mem0 Backend**: Integration with mem0 library as fallback option
- **Memory Manager**: High-level interface with health integration and observability hooks

### Agent System
- **HealthAwareAgent**: Enhanced smolagents wrapper with memory capabilities and health monitoring
- **SecureCoordinator**: Manages multiple agents with shared memory and health checks
- **Native E2B Integration**: Uses smolagents' built-in E2B support for secure execution

### Observability
- **Phoenix Tracing**: Real-time trace monitoring with OTEL integration
- **Memory-Trace Correlation**: Links memory operations with execution traces
- **Performance Metrics**: Latency tracking, success rates, and health scores

## 📈 Monitoring & Evaluation

### Health Monitoring
The system continuously monitors agent performance and automatically gates unhealthy agents:

```bash
# Check agent health status
curl http://localhost:8000/status

# View detailed health metrics in Phoenix UI
# Navigate to http://localhost:6006
```

### Nightly Evaluation
Run the evaluation script to calculate comprehensive health scores:
```bash
python eval_report.py
```

## 🔧 Development & Testing

### Running Tests
```bash
# Run all tests
python -m pytest tests/ -v

# Run specific test suites
python tests/run_mem_o11_tests.py
python tests/test_phoenix_integration.py
```

### Development Mode
```bash
# Start with debug mode and Phoenix integration
source venv/bin/activate
export PHOENIX_ENABLE_TRACING=true
python app.py
```

## 📚 Documentation

- **[Startup Guide](STARTUP_GUIDE.md)**: Detailed setup instructions with troubleshooting
- **[Comprehensive Documentation](docs/COMPREHENSIVE_DOCUMENTATION.md)**: Complete system documentation
- **[Memory & Observability Plan](docs/memory_observability_implementation_plan.md)**: Technical architecture details

## 🚨 Troubleshooting

### Common Issues

**Phoenix Not Starting**
```bash
# Check Docker containers
docker ps --filter "name=phoenix"

# Restart Phoenix
docker-compose -f docker-compose.phoenix.yml down
docker-compose -f docker-compose.phoenix.yml up -d
```

**Missing Dependencies**
```bash
# Reinstall requirements
pip install -r requirements.txt

# Check virtual environment
which python  # Should show venv/bin/python
```

**API Key Issues**
```bash
# Verify .env file
grep -E "(SUPABASE_URL|OPENAI_API_KEY|E2B_API_KEY)" .env
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Run the test suite
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Ready to start trading with AI agents?** Follow the Quick Start guide above and you'll have a fully operational trading agent platform with comprehensive observability and security features!
