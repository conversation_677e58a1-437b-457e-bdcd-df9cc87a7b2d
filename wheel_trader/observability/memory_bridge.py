"""
Memory-Observability Bridge

Links Phoenix traces with memory storage to provide comprehensive
observability for agent memory operations and decision-making processes.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

# Phoenix observability with fallback for compatibility issues
try:
    import phoenix as px
    from phoenix.trace import get_current_span
    PHOENIX_AVAILABLE = True
except (ImportError, SyntaxError):
    PHOENIX_AVAILABLE = False
    # Create fallback implementations
    px = None

    def get_current_span():
        """Fallback function that returns None"""
        return None

from ..memory.manager import MemoryManager
from .tracer import AgentTracer

# Configure logging
logger = logging.getLogger(__name__)


class MemoryObservabilityBridge:
    """
    Bridge between Phoenix tracing and memory storage system
    Automatically captures and stores trace context in memory for agent learning
    """

    def __init__(self, memory_manager: MemoryManager, tracer: AgentTracer):
        """
        Initialize memory-observability bridge

        Args:
            memory_manager: Memory manager instance
            tracer: Agent tracer instance
        """
        self.memory_manager = memory_manager
        self.tracer = tracer
        self.agent_name = tracer.agent_name

        # Bridge statistics
        self.stats = {
            "traces_stored": 0,
            "traces_failed": 0,
            "memory_links_created": 0,
            "last_storage_time": None
        }

        logger.info(f"MemoryObservabilityBridge: Initialized for agent {self.agent_name}")

    def store_trace_memory(self, trace_content: str, trace_metadata: Dict[str, Any],
                          mem_type: str = "trace_execution", importance: float = 0.6) -> Optional[str]:
        """
        Store trace information as memory
        
        Args:
            trace_content: Human-readable description of the trace
            trace_metadata: Trace metadata and context
            mem_type: Type of memory to store
            importance: Importance score for the memory
            
        Returns:
            Memory ID if successful, None if failed
        """
        try:
            # Get current span context if available
            current_span = get_current_span()
            trace_id = None
            span_id = None
            
            if current_span:
                trace_id = current_span.context.trace_id
                span_id = current_span.context.span_id
            
            # Enhance metadata with trace context
            enhanced_metadata = {
                **trace_metadata,
                "stored_by": "memory_bridge",
                "bridge_timestamp": datetime.now().isoformat(),
                "trace_context": {
                    "trace_id": trace_id,
                    "span_id": span_id,
                    "agent_name": self.agent_name
                }
            }
            
            # Store memory
            memory_id = self.memory_manager.store_memory(
                content=trace_content,
                metadata=enhanced_metadata,
                mem_type=mem_type,
                importance=importance,
                trace_id=trace_id,
                span_id=span_id
            )
            
            # Update statistics
            self.stats["traces_stored"] += 1
            self.stats["last_storage_time"] = datetime.now().isoformat()
            
            logger.debug(f"MemoryObservabilityBridge: Stored trace memory {memory_id}")
            return memory_id
            
        except Exception as e:
            self.stats["traces_failed"] += 1
            logger.error(f"MemoryObservabilityBridge: Failed to store trace memory: {e}")
            return None

    def store_execution_result(self, operation: str, result: Any, 
                             execution_time_ms: int, success: bool = True) -> Optional[str]:
        """
        Store execution result as memory
        
        Args:
            operation: Operation that was executed
            result: Result of the execution
            execution_time_ms: Execution time in milliseconds
            success: Whether the execution was successful
            
        Returns:
            Memory ID if successful
        """
        # Create human-readable content
        status = "SUCCESS" if success else "FAILED"
        content = f"Operation: {operation} | Status: {status} | Duration: {execution_time_ms}ms"
        
        if success and result:
            # Add result summary (truncated for readability)
            result_str = str(result)[:200]
            content += f" | Result: {result_str}"
        
        # Prepare metadata
        metadata = {
            "operation": operation,
            "execution_time_ms": execution_time_ms,
            "success": success,
            "result_type": type(result).__name__ if result else None,
            "performance_category": self._categorize_performance(execution_time_ms),
            "market_session": self._get_market_session()
        }
        
        # Add result details if available
        if hasattr(result, '__len__'):
            metadata["result_size"] = len(result)
        
        # Determine importance based on performance and success
        importance = self._calculate_execution_importance(execution_time_ms, success, operation)
        
        return self.store_trace_memory(
            trace_content=content,
            trace_metadata=metadata,
            mem_type="execution_result",
            importance=importance
        )

    def store_error_context(self, operation: str, error: Exception, 
                           context: Dict[str, Any]) -> Optional[str]:
        """
        Store error context as memory for learning
        
        Args:
            operation: Operation that failed
            error: Exception that occurred
            context: Additional context about the error
            
        Returns:
            Memory ID if successful
        """
        # Create descriptive content
        content = f"ERROR in {operation}: {type(error).__name__}: {str(error)}"
        
        # Prepare metadata
        metadata = {
            "operation": operation,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context,
            "market_session": self._get_market_session(),
            "learning_opportunity": True
        }
        
        # High importance for errors to facilitate learning
        return self.store_trace_memory(
            trace_content=content,
            trace_metadata=metadata,
            mem_type="error_log",
            importance=0.8
        )

    def store_decision_context(self, decision: str, reasoning: str, 
                             inputs: Dict[str, Any], confidence: float) -> Optional[str]:
        """
        Store decision-making context as memory
        
        Args:
            decision: Decision that was made
            reasoning: Reasoning behind the decision
            inputs: Input data used for the decision
            confidence: Confidence level in the decision
            
        Returns:
            Memory ID if successful
        """
        # Create content
        content = f"DECISION: {decision} | Reasoning: {reasoning} | Confidence: {confidence:.2f}"
        
        # Prepare metadata
        metadata = {
            "decision": decision,
            "reasoning": reasoning,
            "confidence": confidence,
            "inputs": inputs,
            "decision_timestamp": datetime.now().isoformat(),
            "market_session": self._get_market_session()
        }
        
        # Importance based on confidence level
        importance = min(0.9, 0.5 + (confidence * 0.4))
        
        return self.store_trace_memory(
            trace_content=content,
            trace_metadata=metadata,
            mem_type="agent_action",
            importance=importance
        )

    def link_memories_to_trace(self, memory_ids: List[str], 
                              trace_description: str) -> bool:
        """
        Create links between memories and current trace
        
        Args:
            memory_ids: List of memory IDs to link
            trace_description: Description of the trace
            
        Returns:
            True if successful
        """
        try:
            current_span = get_current_span()
            if not current_span:
                logger.warning("MemoryObservabilityBridge: No current span for memory linking")
                return False
            
            # Add memory links to span attributes
            current_span.set_attribute("linked_memories", memory_ids)
            current_span.set_attribute("memory_link_count", len(memory_ids))
            current_span.set_attribute("memory_link_description", trace_description)
            
            self.stats["memory_links_created"] += len(memory_ids)
            
            logger.debug(f"MemoryObservabilityBridge: Linked {len(memory_ids)} memories to trace")
            return True
            
        except Exception as e:
            logger.error(f"MemoryObservabilityBridge: Failed to link memories to trace: {e}")
            return False

    def get_trace_related_memories(self, trace_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get memories related to a specific trace
        
        Args:
            trace_id: Trace ID to search for
            limit: Maximum number of memories to return
            
        Returns:
            List of related memories
        """
        try:
            # Search for memories with this trace ID
            memories = self.memory_manager.search_memories(
                query="",
                limit=limit,
                filters={"trace_id": trace_id}
            )
            
            return memories
            
        except Exception as e:
            logger.error(f"MemoryObservabilityBridge: Failed to get trace-related memories: {e}")
            return []

    def _categorize_performance(self, execution_time_ms: int) -> str:
        """Categorize performance based on execution time"""
        if execution_time_ms < 100:
            return "fast"
        elif execution_time_ms < 500:
            return "normal"
        elif execution_time_ms < 2000:
            return "slow"
        else:
            return "very_slow"

    def _calculate_execution_importance(self, execution_time_ms: int, 
                                      success: bool, operation: str) -> float:
        """Calculate importance score for execution results"""
        base_importance = 0.5
        
        # Adjust for success/failure
        if not success:
            base_importance += 0.3  # Failures are more important for learning
        
        # Adjust for performance
        if execution_time_ms > 2000:
            base_importance += 0.2  # Slow operations are important
        elif execution_time_ms < 100:
            base_importance += 0.1  # Fast operations are also noteworthy
        
        # Adjust for operation type
        if "trading" in operation.lower() or "decision" in operation.lower():
            base_importance += 0.2  # Trading decisions are critical
        elif "analysis" in operation.lower():
            base_importance += 0.1  # Analysis is important
        
        return min(1.0, base_importance)

    def _get_market_session(self) -> str:
        """Get current market session"""
        now = datetime.now()
        hour = now.hour
        
        if 4 <= hour < 9:
            return "pre_market"
        elif 9 <= hour < 16:
            return "regular_hours"
        elif 16 <= hour < 20:
            return "after_hours"
        else:
            return "closed"

    def get_bridge_stats(self) -> Dict[str, Any]:
        """Get bridge statistics"""
        total_operations = self.stats["traces_stored"] + self.stats["traces_failed"]
        success_rate = 0
        
        if total_operations > 0:
            success_rate = self.stats["traces_stored"] / total_operations
        
        return {
            "agent_name": self.agent_name,
            "traces_stored": self.stats["traces_stored"],
            "traces_failed": self.stats["traces_failed"],
            "memory_links_created": self.stats["memory_links_created"],
            "success_rate": success_rate,
            "last_storage_time": self.stats["last_storage_time"],
            "bridge_healthy": success_rate > 0.9
        }


def auto_store_trace_memory(mem_type: str = "trace_execution", 
                           importance: float = 0.6):
    """
    Decorator to automatically store trace information as memory
    
    Args:
        mem_type: Type of memory to store
        importance: Importance score for the memory
    """
    def decorator(func):
        def wrapper(self, *args, **kwargs):
            # Check if bridge is available
            bridge = getattr(self, 'memory_bridge', None)
            if not bridge:
                return func(self, *args, **kwargs)
            
            # Execute function and capture result
            start_time = time.time()
            try:
                result = func(self, *args, **kwargs)
                execution_time = int((time.time() - start_time) * 1000)
                
                # Store execution result
                bridge.store_execution_result(
                    operation=func.__name__,
                    result=result,
                    execution_time_ms=execution_time,
                    success=True
                )
                
                return result
                
            except Exception as e:
                execution_time = int((time.time() - start_time) * 1000)
                
                # Store error context
                bridge.store_error_context(
                    operation=func.__name__,
                    error=e,
                    context={
                        "args_count": len(args),
                        "kwargs_keys": list(kwargs.keys()),
                        "execution_time_ms": execution_time
                    }
                )
                
                raise
        
        return wrapper
    return decorator


# Export main classes and functions
__all__ = [
    "MemoryObservabilityBridge",
    "auto_store_trace_memory"
]
