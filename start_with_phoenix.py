#!/usr/bin/env python3
"""
CoveredCalls-Agents Startup Script with Phoenix Integration

This script provides a comprehensive startup process that:
1. Validates Phoenix observability setup
2. Configures environment variables
3. Tests Phoenix connectivity
4. Starts the application with proper observability integration
"""

import os
import sys
import time
import logging
import subprocess
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def print_banner():
    """Print startup banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                   CoveredCalls-Agents                        ║
    ║              Phoenix Observability Startup                   ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_environment():
    """Check if .env file exists and has required variables"""
    logger.info("🔍 Checking environment configuration...")
    
    env_file = Path(".env")
    if not env_file.exists():
        logger.warning("⚠️ .env file not found. Creating from template...")
        
        # Copy .env.example to .env if it doesn't exist
        example_file = Path(".env.example")
        if example_file.exists():
            import shutil
            shutil.copy(example_file, env_file)
            logger.info("✅ Created .env from .env.example")
            logger.warning("🔧 Please edit .env file with your actual API keys and configuration")
            return False
        else:
            logger.error("❌ No .env.example file found. Cannot create .env")
            return False
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check required variables
    required_vars = [
        "SUPABASE_URL",
        "SUPABASE_KEY", 
        "OPENAI_API_KEY",
        "E2B_API_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var) or os.getenv(var).startswith("your_"):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {missing_vars}")
        logger.error("🔧 Please edit .env file with your actual API keys")
        return False
    
    logger.info("✅ Environment configuration validated")
    return True

def check_phoenix_status():
    """Check if Phoenix is running and accessible"""
    logger.info("🔍 Checking Phoenix observability status...")
    
    try:
        import requests
        
        # Check if Phoenix UI is accessible
        phoenix_url = os.getenv("PHOENIX_COLLECTOR_ENDPOINT", "http://localhost:6006")
        ui_url = phoenix_url.replace("/v1/traces", "")
        
        response = requests.get(f"{ui_url}/health", timeout=5)
        if response.status_code == 200:
            logger.info("✅ Phoenix UI is accessible")
        else:
            logger.warning(f"⚠️ Phoenix UI returned status {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        logger.warning("⚠️ Phoenix UI not accessible - checking Docker containers...")
        
        # Check if Phoenix container is running
        try:
            result = subprocess.run(
                ["docker", "ps", "--filter", "name=phoenix", "--format", "table {{.Names}}\t{{.Status}}"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if "phoenix" in result.stdout and "Up" in result.stdout:
                logger.info("✅ Phoenix Docker container is running")
                logger.info("⏳ Waiting for Phoenix to be fully ready...")
                time.sleep(5)
            else:
                logger.warning("⚠️ Phoenix container not found or not running")
                logger.info("🚀 Starting Phoenix with docker-compose...")
                
                # Start Phoenix
                start_result = subprocess.run(
                    ["docker-compose", "-f", "docker-compose.phoenix.yml", "up", "-d"],
                    capture_output=True,
                    text=True,
                    timeout=60
                )
                
                if start_result.returncode == 0:
                    logger.info("✅ Phoenix started successfully")
                    logger.info("⏳ Waiting for Phoenix to be ready...")
                    time.sleep(10)
                else:
                    logger.error(f"❌ Failed to start Phoenix: {start_result.stderr}")
                    return False
                    
        except subprocess.TimeoutExpired:
            logger.error("❌ Timeout while checking/starting Phoenix")
            return False
        except FileNotFoundError:
            logger.error("❌ Docker not found. Please install Docker to run Phoenix")
            return False
    
    except Exception as e:
        logger.warning(f"⚠️ Could not verify Phoenix status: {e}")
    
    return True

def start_application():
    """Start the CoveredCalls-Agents application"""
    logger.info("🚀 Starting CoveredCalls-Agents application...")
    
    try:
        # Set Phoenix environment variables if not already set
        if not os.getenv("PHOENIX_COLLECTOR_ENDPOINT"):
            os.environ["PHOENIX_COLLECTOR_ENDPOINT"] = "http://localhost:6006/v1/traces"
        
        if not os.getenv("PHOENIX_PROJECT_NAME"):
            os.environ["PHOENIX_PROJECT_NAME"] = "coveredcalls-agents"
        
        if not os.getenv("PHOENIX_ENABLE_TRACING"):
            os.environ["PHOENIX_ENABLE_TRACING"] = "true"
        
        logger.info("🔧 Phoenix environment variables configured")
        logger.info(f"   - Endpoint: {os.getenv('PHOENIX_COLLECTOR_ENDPOINT')}")
        logger.info(f"   - Project: {os.getenv('PHOENIX_PROJECT_NAME')}")
        logger.info(f"   - Tracing: {os.getenv('PHOENIX_ENABLE_TRACING')}")
        
        # Import and start the Flask application
        logger.info("📡 Initializing Phoenix observability...")
        
        # Import the app (this will initialize Phoenix)
        from app import app
        
        logger.info("✅ Application initialized with Phoenix observability")
        logger.info("🌐 Starting Flask server...")
        logger.info("📊 Phoenix UI: http://localhost:6006")
        logger.info("🖥️  Application: http://localhost:8000")
        
        # Start the Flask application
        app.run(host='0.0.0.0', port=8000, debug=True)
        
    except KeyboardInterrupt:
        logger.info("🛑 Application stopped by user")
    except Exception as e:
        logger.error(f"❌ Failed to start application: {e}")
        return False
    
    return True

def main():
    """Main startup process"""
    print_banner()
    
    # Step 1: Check environment
    if not check_environment():
        logger.error("❌ Environment check failed. Please fix configuration and try again.")
        return 1
    
    # Step 2: Check Phoenix status
    if not check_phoenix_status():
        logger.error("❌ Phoenix setup failed. Please check Docker and try again.")
        return 1
    
    # Step 3: Start application
    logger.info("🎯 All checks passed. Starting application...")
    start_application()
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
