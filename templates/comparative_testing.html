<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comparative Agent Testing - Covered Calls Platform</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            max-width: 1200px; 
            margin: 2rem auto; 
            padding: 0 1rem; 
            background-color: #f9f9f9; 
        }
        h1, h2, h3 { color: #222; }
        h1 { text-align: center; border-bottom: 2px solid #eee; padding-bottom: 0.5rem; }
        .container { 
            background-color: #fff; 
            padding: 2rem; 
            border-radius: 8px; 
            box-shadow: 0 4px 8px rgba(0,0,0,0.05); 
            margin-bottom: 2rem;
        }
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
            text-align: center;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .custom-input {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
        }
        .custom-input input {
            flex: 1;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
        }
        .custom-input button {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
        }
        .results-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        .agent-card {
            background: #fff;
            border: 2px solid #eee;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .agent-card.conservative {
            border-color: #ffc107;
            background: linear-gradient(135deg, #fff9e6 0%, #fff 100%);
        }
        .agent-card.aggressive {
            border-color: #dc3545;
            background: linear-gradient(135deg, #ffe6e6 0%, #fff 100%);
        }
        .agent-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        .agent-icon {
            font-size: 1.5rem;
        }
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        .status-loading {
            background: #d1ecf1;
            color: #0c5460;
        }
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .phoenix-link {
            background: #6f42c1;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            display: inline-block;
            margin: 1rem 0;
            font-weight: 600;
        }
        .phoenix-link:hover {
            background: #5a2d91;
            color: white;
            text-decoration: none;
        }
        .back-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 Agent Testing</h1>
        <p><a href="/" class="back-link">← Back to Home</a></p>

        <p>Test the <strong>Conservative</strong> trading agent with custom tasks.
        The Conservative agent excludes biotech stocks and focuses on safer trading opportunities.</p>

        <div class="button-grid">          
            <button class="test-button" onclick="checkAgentStatus()">
                ❤️ Check Agent Status<br>
                <small>View agent health and availability</small>
            </button>
            
            <div style="grid-column: 1 / -1;">
                <h3>Custom Test</h3>
                <div class="custom-input">
                    <input type="text" id="customTask" placeholder="Enter custom task (e.g., 'Analyze TSLA for options opportunities')" />
                    <button onclick="runCustomTest()">Run Test</button>
                </div>
            </div>
        </div>

        <a href="http://localhost:6006" target="_blank" class="phoenix-link">
            📊 View Traces in Phoenix UI
        </a>
    </div>

    <div id="resultsContainer" style="display: none;">
        <div class="container">
            <h2>📋 Test Results</h2>
            <div id="taskInfo"></div>
            <div class="results-container">
                <div class="agent-card conservative">
                    <div class="agent-header">
                        <span class="agent-icon">🔒</span>
                        <h3>Conservative Agent</h3>
                        <span id="conservativeStatus" class="status-badge"></span>
                    </div>
                    <div id="conservativeResult"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showLoading() {
            document.getElementById('resultsContainer').style.display = 'block';
            document.getElementById('taskInfo').innerHTML = '<div class="loading"><div class="spinner"></div>Running test...</div>';
            document.getElementById('conservativeStatus').className = 'status-badge status-loading';
            document.getElementById('conservativeStatus').textContent = 'Running...';
            document.getElementById('conservativeResult').innerHTML = '<div class="loading">Executing in E2B sandbox...</div>';

            // Disable all buttons
            document.querySelectorAll('.test-button, .custom-input button').forEach(btn => btn.disabled = true);
        }

        function hideLoading() {
            // Re-enable all buttons
            document.querySelectorAll('.test-button, .custom-input button').forEach(btn => btn.disabled = false);
        }

        function displayResults(data) {
            document.getElementById('taskInfo').innerHTML = `<p><strong>Task:</strong> ${data.task}</p>`;
            
            // Conservative agent results
            const conservativeResult = data.results.conservative;
            if (conservativeResult.success) {
                document.getElementById('conservativeStatus').className = 'status-badge status-success';
                document.getElementById('conservativeStatus').textContent = 'Success';
                document.getElementById('conservativeResult').innerHTML = `
                    <p><strong>Result:</strong></p>
                    <pre>${JSON.stringify(conservativeResult.result, null, 2)}</pre>
                `;
            } else {
                document.getElementById('conservativeStatus').className = 'status-badge status-error';
                document.getElementById('conservativeStatus').textContent = 'Error';
                document.getElementById('conservativeResult').innerHTML = `
                    <p><strong>Error:</strong> ${conservativeResult.error}</p>
                `;
            }

        }

        function runCustomTest() {
            const task = document.getElementById('customTask').value.trim();
            if (!task) {
                alert('Please enter a task');
                return;
            }
            
            showLoading();
            
            fetch('/api/test-custom', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ task: task })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    displayResults(data);
                } else {
                    alert('Test failed: ' + data.error);
                }
            })
            .catch(error => {
                hideLoading();
                alert('Error: ' + error.message);
            });
        }

        function checkAgentStatus() {
            showLoading();

            fetch('/api/agent-status')
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    document.getElementById('taskInfo').innerHTML = '<p><strong>Agent Status Check</strong></p>';

                    const conservativeAgent = data.agents.conservative;

                    document.getElementById('conservativeStatus').className = conservativeAgent.healthy ? 'status-badge status-success' : 'status-badge status-error';
                    document.getElementById('conservativeStatus').textContent = conservativeAgent.healthy ? 'Healthy' : 'Unhealthy';
                    document.getElementById('conservativeResult').innerHTML = `
                        <p><strong>Name:</strong> ${conservativeAgent.name}</p>
                        <p><strong>Tools:</strong> ${conservativeAgent.tools_count}</p>
                        <p><strong>Health:</strong> ${conservativeAgent.healthy ? '✅ Good' : '❌ Poor'}</p>
                    `;

                    document.getElementById('resultsContainer').style.display = 'block';
                } else {
                    alert('Status check failed: ' + data.error);
                }
            })
            .catch(error => {
                hideLoading();
                alert('Error: ' + error.message);
            });
        }
    </script>
</body>
</html>
