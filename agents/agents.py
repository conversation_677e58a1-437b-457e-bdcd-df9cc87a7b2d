"""
Agent Creation and Management
memory, observability.
This module provides functions to create different types of trading agents
with various configurations and filtering strategies.
"""

from wheel_trader.smolagents_e2b import HealthAwareAgent
from .tools import (
    conservative_market_analysis_tool,
    market_analysis_tool,
    options_data_tool,
    risk_assessment_tool,
    get_current_date
)


def create_trading_agent(name: str = "trading_agent", model=None) -> HealthAwareAgent:
    """
    Create a health-aware trading agent with E2B execution and domain-specific tools.

    Args:
        name: Name for the agent
        model: Model to use (defaults to InferenceClientModel)

    Returns:
        HealthAwareAgent configured for trading tasks
    """
    tools = [
        options_data_tool,
        market_analysis_tool,
        risk_assessment_tool,
        get_current_date
    ]

    agent = HealthAwareAgent(
            name=name,
            model=model,
            tools=tools,
            additional_imports=["supabase", "python_dotenv"],
            health_threshold=0.7,
            memory_backend="pgvector",
            enable_memory=True,
            enable_observability=True
        )

    return agent


def create_conservative_agent(name: str = "conservative_agent", model=None) -> HealthAwareAgent:
    """
    Create a conservative trading agent that excludes biotech stocks.

    Args:
        name: Name for the agent

    Returns:
        HealthAwareAgent configured with biotech exclusion
    """
    tools = [
        conservative_market_analysis_tool,
        options_data_tool,
        risk_assessment_tool,
        get_current_date
    ]

    # For E2B compatibility, we need to handle environment gracefully
    try:
        agent = HealthAwareAgent(
            name=name,
            model=model,
            tools=tools,
            additional_imports=["supabase", "python_dotenv"],
            health_threshold=0.7,
            memory_backend="pgvector",
            enable_memory=True,
            enable_observability=True
        )
    except Exception as e:
        import logging
        logging.warning(f"Creating agent with limited features due to: {e}")
        agent = HealthAwareAgent(
            name=name,
            model=model,
            tools=tools,
            health_threshold=0.7,
            memory_backend=None,
            enable_observability=False
        )

    return agent


def run_comparative_test(task: str) -> dict:
    """
    Run the same task on conservative agent.

    Args:
        task: The trading task to execute (e.g., "Analyze MRNA for trading opportunities")

    Returns:
        Dictionary with results from conservative agent
    """
    print(f"🧪 Running Comparative Test: {task}")
    print("=" * 50)

    conservative = create_conservative_agent()

    results = {}

    # Run task on conservative agent
    print("🔒 Running Conservative Agent...")
    try:
        conservative_result = conservative.run(task)
        results["conservative"] = {
            "success": True,
            "result": conservative_result,
            "agent_name": conservative.name
        }
        print("✅ Conservative completed")
    except Exception as e:
        results["conservative"] = {
            "success": False,
            "error": str(e),
            "agent_name": conservative.name
        }
        print(f"❌ Conservative failed: {e}")

    return results
