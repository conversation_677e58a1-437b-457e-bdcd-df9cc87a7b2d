"""
Options Data Module

This module provides functions for fetching and analyzing options data.
It's used by various test files and provides a compatibility layer.
"""

from .tools import options_data_tool, market_analysis_tool, risk_assessment_tool

# Re-export tools for backward compatibility
__all__ = [
    'options_data_tool',
    'market_analysis_tool', 
    'risk_assessment_tool',
    'get_options_data',
    'analyze_market',
    'assess_risk'
]

# Compatibility functions
def get_options_data(symbol: str, expiry_date: str) -> dict:
    """
    Compatibility wrapper for options_data_tool
    
    Args:
        symbol: Stock symbol (e.g., AAPL)
        expiry_date: Expiration date in YYYY-MM-DD format
        
    Returns:
        Dictionary containing options data
    """
    return options_data_tool(symbol, expiry_date)

def analyze_market(symbol: str, analysis_type: str = "rsi") -> dict:
    """
    Compatibility wrapper for market_analysis_tool
    
    Args:
        symbol: Stock symbol (e.g., AAPL)
        analysis_type: Type of analysis to perform
        
    Returns:
        Dictionary containing market analysis
    """
    return market_analysis_tool(symbol, analysis_type)

def assess_risk(symbol: str, position_size: float, portfolio_value: float) -> dict:
    """
    Compatibility wrapper for risk_assessment_tool
    
    Args:
        symbol: Stock symbol (e.g., AAPL)
        position_size: Size of the position
        portfolio_value: Total portfolio value
        
    Returns:
        Dictionary containing risk assessment
    """
    return risk_assessment_tool(symbol, position_size, portfolio_value)
