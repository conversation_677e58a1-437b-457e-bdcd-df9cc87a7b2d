from flask import Flask, jsonify, render_template, request

from agents.agents import run_comparative_test
from wheel_trader.coordinator import coordinator_app

app = Flask(__name__)

# Register the coordinator blueprint
app.register_blueprint(coordinator_app)


@app.route('/')
def index():
    """Serve the main documentation page"""
    return render_template('index.html')


@app.route('/comparative-testing')
def comparative_testing():
    """Serve the comparative testing page"""
    return render_template('comparative_testing.html')


@app.route('/api/test-custom', methods=['POST'])
def test_custom():
    """Run comparative test on custom task"""
    try:
        data = request.get_json()
        task = data.get('task', '')

        if not task:
            return jsonify({
                "success": False,
                "error": "Task is required"
            }), 400

        results = run_comparative_test(task)
        return jsonify({
            "success": True,
            "task": task,
            "results": results
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


@app.route('/api/agent-status', methods=['GET'])
def agent_status():
    """Get status of both agents"""
    try:
        agents = create_comparative_agent_pair()
        status = {}

        for agent_type, agent in agents.items():
            status[agent_type] = {
                "name": agent.name,
                "healthy": agent.health_manager.is_agent_healthy(agent.name),
                "tools_count": len(agent.agent.tools)
            }

        return jsonify({
            "success": True,
            "agents": status
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500


# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('index.html'), 404


@app.errorhandler(500)
def internal_error(error):
    return render_template('index.html'), 500


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8000, debug=True)
